<?php
namespace app\index\middleware;

use app\common\repository\UserRepository;
use Closure;
use think\exception\HttpResponseException;
use think\facade\Config;
use think\helper\Str;
use think\Request;
use think\Response;
use think\response\Html;


/**
 * 登入中间件
 * Class LoginMiddleware
 * @package app\home\middleware
 */
class LoginMiddleware
{

    /**
     * 处理请求
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next):Response
    {

        $accessUri  = Config::get('access_uri');

        $reqUri     = \think\facade\Request::baseUrl();

        //白名单接口
        foreach ($accessUri as $item)
        {
            if (Str::contains($reqUri, $item))
            {
                return $next($request);
            }
        }


        //非名单接口
        $UserRepo  = new UserRepository();
        $res       = $UserRepo->userByHeader('id');

        if (empty($res))
        {
            $info     = json_encode(['code' => 444, '登录失效,请重新登录','data'=>[]],320);
            $type     = array("Content-type", "application/json");
            $response = Html::create('','json',256)->content($info)->header($type);

            throw new HttpResponseException($response);
        }

        return $next($request);
    }

}