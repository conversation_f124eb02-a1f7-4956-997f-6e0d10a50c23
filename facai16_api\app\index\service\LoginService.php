<?php
namespace app\index\service;


use app\common\jobs\RegisterGiftJob;
use app\common\repository\LevelRepository;
use app\common\repository\MoneyRepository;
use app\common\repository\SmsCodeRepository;
use app\common\repository\UserInfoRepository;
use app\common\repository\UserLoginRepository;
use app\common\repository\UserRelationRepository;
use app\common\repository\UserRepository;
use app\common\repository\UserStateRepository;
use app\common\repository\UserWordsRepository;
use app\common\utils\IpAddress;
use app\common\utils\Record;
use app\common\utils\Result;
use Exception;
use think\facade\Db;
use think\facade\Request;
use Zhuzhichao\IpLocationZh\Ip;


/**
 * 登入服务
 * Class loginService
 * @package app\home\service
 */
class LoginService
{
    /**
     * 登入
     * @param string $phone
     * @param string $password
     * @return array
     */
    public function login(string $phone, string $password): array
    {
        $UserRepo       = new UserRepository();
        $UserLoginRepo  = new UserLoginRepository();

        $where1    = [];
        $where1[]  = ['phone', '=', $phone];
        $where1[]  = ['password', '=', $password];

        $where2    = [];
        $where2[]  = ['username', '=', $phone];
        $where2[]  = ['password', '=', $password];

        $user      = $UserRepo->findByWhereOr([$where1, $where2]);

        //账号或者密码错误
        if(empty($user))
        {
            return Result::fail('账号或者密码错误');
        }

        $ip         = IpAddress::getIp();
        $time       = Request::time();
        $token      = md5($time . $user['id']);
        $address    = join(',', array_filter(Ip::find($ip)));

        $update     = [
            'login_ip'      => $ip,
            'ip_address'    => $address,
            'login_time'    => $time,
            'token'         => $token,
        ];

        $res           = $UserRepo->updateById($user['id'], $update);


        //登入失败
        if (!$res)
        {
            return Result::fail('登入失败');
        }

        $insert = [
            'uid'           => $user['id'],
            'username'      => $user['username'],
            'phone'         => $user['phone'],
            'ip'            => $ip,
            'ip_address'    => $address,
            'is_test'       => $user['is_test'],
            'create_time'   => $time,
            'update_time'   => $time,
        ];

        $res = $UserLoginRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail('写入日志失败');
        }

        return Result::success(['token' => $token], __('login_successful'));
    }

    /**
     * 注册
     * @param string $password
     * @param string $phone
     * @param string $code
     * @param string $inviteCode
     * @param string $withdraw
     * @param string $username
     * @return array
     */
    public function register(string $password, string $phone, string $code, string $inviteCode, string $withdraw, string $username): array
    {

         $UserRepo          = new UserRepository();
         $SmsCodeRepo       = new SmsCodeRepository();
         $UserStateRepo     = new UserStateRepository();
         $MoneyRepo         = new MoneyRepository();
         $UserInfoRepo      = new UserInfoRepository();
         $UserWordsRepo     = new UserWordsRepository();
         $UserRelationRepo  = new UserRelationRepository();

        // 请输入6~15位密码
        if(strlen($password) < 6 || strlen($password) > 15)
        {
            return Result::fail('请输入6~15位密码');
        }

        //请输入正确的手机号
        if(strlen($phone) != 11 || !is_numeric($phone))
        {
            return Result::fail('请输入正确的手机号');
        }

        if (strlen($withdraw) != 6)
        {
            return Result::fail('取款密码必须为6位');
        }

        $where      = [];
        $where[]    = ['phone', '=', $phone];
        $user       = $UserRepo->findByCondition($where);

        //手机号已被注册
        if($user)
        {
            return Result::fail('手机号已被注册');
        }


//        $env         = env('app_env');
//
//        if ($env == 'prod' && $code != '888999')
//        {
//            $where       = [];
//            $where[]     = ['phone', '=', $phone];
//            $where[]     = ['code', '=', $code];
//            $smsCode     = $SmsCodeRepo->findByCondition($where,'*',['create_time' => 'desc']);
//
//            //请输入正确的验证码
//            if(empty($smsCode))
//            {
//                return Result::fail('请输入正确的验证码');
//            }
//
//            //验证码已过期，请重新发送验证码
//            if($smsCode['create_time'] < (time() - 300))
//            {
//                return Result::fail('验证码已过期，请重新发送验证码');
//            }
//        }

        $where     = [];
        $where[]   = ['invite', '=', $inviteCode];
        $pUser     = $UserRepo->findByCondition($where);

        $banInvite = $UserStateRepo->valueByCondition(['uid' => $pUser['id'] ?? 0], 'ban_invite');


        //邀请码不存在或被冻结
        if(empty($pUser) || $banInvite)
        {
            return Result::fail('邀请码不存在或被冻结');
        }



        Db::startTrans();

        try {


            $insert = [
                'username'      => $username,
                'nickname'      => $username,
                'phone'         => $phone,
                'invite'        => $UserRepo->createCode(),
                'level'         => 0,
                'level_name'    => '',
                'password'      => $password,
                'pin'           => $withdraw,
                'register_ip'   => IpAddress::realIP(),
                'create_time'   => Request::time(),
                'update_time'   => Request::time(),
                'v1_id'         => $pUser['id'] ?? 0,
                'v1_name'       => $pUser['phone'] ?? '',
                'v2_id'         => $pUser['v1_id'] ?? 0,
                'v2_name'       => $pUser['v1_name'] ?? '',
                'v3_id'         => $pUser['v2_id'] ?? 0,
                'v3_name'       => $pUser['v2_name'] ?? '',
            ];

            $uid      = $UserRepo->insertsGetId($insert);

            if (!$uid)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }


            $insert = [
                'uid'           => $uid,
                'create_time'   => Request::time(),
                'update_time'   => Request::time(),
            ];

           $res =  $UserInfoRepo->inserts($insert);


            if (!$res)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }


            $res =  $UserStateRepo->inserts($insert);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }


            $res = $MoneyRepo->inserts($insert);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }

            $insert = [
                'uid'           => $uid,
                'username'      => $username,
                'phone'         => $phone,
                'create_time'   => Request::time(),
                'update_time'   => Request::time(),
            ];

            $res = $UserWordsRepo->inserts($insert);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }

            $insert   = [];

            $insert[] = [
                'uid'           => $uid,
                'top_id'        => $pUser['id'],
                'level'         => 1,
                'create_time'   => Request::time(),
                'update_time'   => Request::time(),
            ];

            $where      = [];
            $where[]    = ['uid' , '=', $pUser['id']];

            $relations  = $UserRelationRepo->selectByCondition($where);

            foreach ($relations as $val)
            {
                $insert[] = [
                    'uid'           => $uid,
                    'top_id'        => $val['top_id'],
                    'level'         => $val['level'] + 1,
                    'create_time'   => Request::time(),
                    'update_time'   => Request::time(),
                ];
            }

            $res = $UserRelationRepo->insertsAll($insert);

            if (!$res)
            {
                Db::rollback();
                return Result::fail('保存失败');
            }

            // 提交事务
            Db::commit();




        } catch (Exception $exception)
        {

            Record::exception('index', $exception,'LoginService->register');

            Db::rollback();

            //注册失败
            return Result::fail('注册失败');
        }


        $userInfo   = $UserRepo->findById($uid);
        $ip         = IpAddress::getIp();
        $time       = Request::time();
        $token      = md5($time . $userInfo['id']);
        $address    = join(',', array_filter(Ip::find($ip)));

        $update     = [
            'login_ip'      => $ip,
            'ip_address'    => $address,
            'login_time'    => $time,
            'token'         => $token,
        ];

        $res           = $UserRepo->updateById($userInfo['id'], $update);

        $UserStateRepo = new UserStateRepository();

        $isTest        = $UserStateRepo->valueByCondition(['uid' => $userInfo['id']],'is_test');


        //登入失败
        if (!$res)
        {
            return Result::fail('登入失败');
        }

        $insert = [
            'uid'           => $userInfo['id'],
            'username'      => $userInfo['username'],
            'phone'         => $userInfo['phone'],
            'ip'            => $ip,
            'ip_address'    => $address,
            'is_test'       => $isTest,
            'create_time'   => $time,
            'update_time'   => $time,
        ];

        $UserLoginRepo = new UserLoginRepository();

        $res = $UserLoginRepo->inserts($insert);

        if (!$res)
        {
            return Result::fail('写入日志失败');
        }

        return Result::success(['token' => $token],'注册成功');
    }


    /**
     * 修改密码
     * @param string $phone
     * @param string $password
     * @param string $code
     * @return array
     */
    public function reset(string $phone, string $password, string $code): array
    {

        $SmsCodeRepo      = new SmsCodeRepository();
        $UserRepo         = new UserRepository();

        $where      = [];
        $where[]    = ['phone', '=', $phone];
        $user       = $UserRepo->findByCondition($where);

        if(empty($user))
        {
            return Result::fail('没有此用户信息');
        }

        if(strlen($password) != 6 )
        {
           return Result::fail(' 请输入6位密码');
        }


        $where       = [];
        $where[]     = ['phone', '=', $phone];
        $where[]     = ['code', '=', $code];
        $smsCode     = $SmsCodeRepo->findByCondition($where);

        $env         = env('APP_ENV');

        //请输入正确的验证码
        if($env == 'prod' && empty($smsCode))
        {
            //return Result::fail('请输入正确的验证码');
        }

        $update = [
            'password'      => $password,
            'update_time'   => Request::time(),
        ];


        $res = $UserRepo->updateById($user['id'], $update);

        if (!$res)
        {
            return Result::fail('保存失败');
        }

        return Result::success();
    }




}